{"build": {"arduino": {"ldscript": "esp32s3_out.ld", "memory_type": "qio_opi"}, "core": "esp32", "extra_flags": ["-DBOARD_HAS_PSRAM"], "f_cpu": "240000000L", "f_flash": "80000000L", "flash_mode": "qio", "mcu": "esp32s3", "variant": "esp32s3"}, "connectivity": ["wifi", "bluetooth"], "debug": {"openocd_target": "esp32s3.cfg"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "espidf"], "name": "ESP32-S3 Panel 4848S040 (16MB Flash, 8MB PSRAM)", "upload": {"flash_size": "16MB", "maximum_ram_size": 327680, "maximum_size": 16777216, "require_upload_port": true, "speed": 460800}, "url": "https://www.github.com/yawom", "vendor": "yawom"}