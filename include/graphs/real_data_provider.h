#ifndef REAL_DATA_PROVIDER_H
#define REAL_DATA_PROVIDER_H

#include "graphs/data_provider.h"

/**
 * Real data provider
 * This is a placeholder for a real data provider that would get data from sensors or other sources
 */
class RealDataProvider : public DataProvider {
public:
    /**
     * Constructor
     * @param dataSize The number of data points to maintain
     * @param maxValue The maximum absolute value for data points
     */
    RealDataProvider(int dataSize, int maxValue)
        : DataProvider(dataSize, maxValue) {
    }
    
    /**
     * Initialize the data provider
     */
    void initialize() override {
        // Initialize with zeros or some default values
        for (int i = 0; i < dataSize; i++) {
            data[i] = 0;
        }
    }
    
    /**
     * Update the data
     * In a real implementation, this would get data from sensors or other sources
     */
    void update() override {
        // Shift data to the left
        for (int i = 1; i < dataSize; i++) {
            data[i - 1] = data[i];
        }
        
        // In a real implementation, this would get the latest data point from a sensor
        // For now, just use a random value as a placeholder
        int8_t newValue = random(-maxValue, maxValue + 1);
        
        // Store the new value
        data[dataSize - 1] = newValue;
    }
    
    /**
     * Add a new data point
     * This method would be called when new data is available from a sensor
     * @param value The new data point
     */
    void addDataPoint(int8_t value) {
        // Shift data to the left
        for (int i = 1; i < dataSize; i++) {
            data[i - 1] = data[i];
        }
        
        // Store the new value
        data[dataSize - 1] = value;
    }
};

#endif // REAL_DATA_PROVIDER_H
