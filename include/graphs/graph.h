#ifndef GRAPH_H
#define GRAPH_H

#include <Arduino.h>
#include "display/board_display.h"
#include "graphs/data_provider.h"
#include "graphs/wave_data_provider.h"

class Graph
{
private:
    BOARD_LGFX& gfx;
    int width;
    int height;
    DataProvider* dataProvider;
    bool ownsDataProvider; // Whether this Graph owns the DataProvider and should delete it

    uint16_t positiveColor;
    uint16_t negativeColor;
    uint16_t backgroundColor;
    uint16_t borderColor;
    bool showBorder;

public:
    /**
     * Constructor with a specific data provider
     */
    Graph(BOARD_LGFX& gfx, int width, int height, DataProvider* dataProvider, bool ownsDataProvider = true, bool showBorder = false);

    /**
     * Destructor
     */
    ~Graph();

    /**
     * Set the colors for the graph
     */
    void setColors(uint16_t positiveColor, uint16_t negativeColor, uint16_t backgroundColor, uint16_t borderColor = TFT_DARKGREY);

    /**
     * Set whether to show a border around the graph
     */
    void setBorder(bool showBorder);

    /**
     * Draw the graph at the specified position
     */
    void draw(int x, int y);

    /**
     * Update the data in the graph
     */
    void updateData();

    /**
     * Set the data provider
     * If ownsDataProvider is true, the Graph will delete the old provider and take ownership of the new one
     */
    void setDataProvider(DataProvider* dataProvider, bool ownsDataProvider = true);

    /**
     * Get the data provider
     */
    DataProvider* getDataProvider() const;

    /**
     * Get the width of the graph
     */
    int getWidth() const { return width; }

    /**
     * Get the height of the graph
     */
    int getHeight() const { return height; }

    /**
     * Convenience method to set wave pattern if using a WaveDataProvider
     */
    void setWavePattern(WaveDataProvider::WavePattern pattern);

    /**
     * Convenience method to set wave parameters if using a WaveDataProvider
     */
    void setWaveParameters(float frequency, float amplitude, float phaseShift = 0.0);
};

#endif // GRAPH_H
