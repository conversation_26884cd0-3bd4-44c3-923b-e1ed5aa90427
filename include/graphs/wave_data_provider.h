#ifndef WAVE_DATA_PROVIDER_H
#define WAVE_DATA_PROVIDER_H

#include "graphs/data_provider.h"

/**
 * Wave pattern data provider
 * Generates various wave patterns (sine, cosine, combined)
 */
class WaveDataProvider : public DataProvider {
public:
    /**
     * Wave pattern types
     */
    enum WavePattern {
        SINE_WAVE,
        COSINE_WAVE,
        COMBINED_WAVE,
        RANDOM_WAVE
    };
    
    /**
     * Constructor
     * @param dataSize The number of data points to maintain
     * @param maxValue The maximum absolute value for data points
     * @param pattern The wave pattern to generate
     */
    WaveDataProvider(int dataSize, int maxValue, WavePattern pattern = SINE_WAVE)
        : DataProvider(dataSize, maxValue), 
          pattern(pattern), 
          angle(0.0), 
          frequency(0.2), 
          amplitude(maxValue * 0.8), 
          phaseShift(0.0) {
    }
    
    /**
     * Initialize the data provider with a wave pattern
     */
    void initialize() override {
        for (int i = 0; i < dataSize; i++) {
            float x = (float)i / dataSize * 2 * PI;
            switch (pattern) {
                case SINE_WAVE:
                    data[i] = (int8_t)(amplitude * sin(x + phaseShift));
                    break;
                case COSINE_WAVE:
                    data[i] = (int8_t)(amplitude * cos(x + phaseShift));
                    break;
                case COMBINED_WAVE:
                    data[i] = (int8_t)(amplitude * 0.5 * (sin(x + phaseShift) + cos(x * 2)));
                    break;
                case RANDOM_WAVE:
                    data[i] = random(-maxValue/2, maxValue/2);
                    break;
            }
        }
    }
    
    /**
     * Update the data with the next wave value
     */
    void update() override {
        // Shift data to the left
        for (int i = 1; i < dataSize; i++) {
            data[i - 1] = data[i];
        }
        
        // Calculate new point based on the selected wave pattern
        float newValue = 0.0;
        
        switch (pattern) {
            case SINE_WAVE:
                newValue = amplitude * sin(angle + phaseShift);
                break;
            case COSINE_WAVE:
                newValue = amplitude * cos(angle + phaseShift);
                break;
            case COMBINED_WAVE:
                newValue = amplitude * 0.5 * (sin(angle + phaseShift) + cos(angle * 2));
                break;
            case RANDOM_WAVE:
                // Add some smoothing to random values
                newValue = data[dataSize - 2] + random(-5, 6);
                // Constrain to amplitude range
                if (newValue > amplitude) newValue = amplitude;
                if (newValue < -amplitude) newValue = -amplitude;
                break;
        }
        
        // Increment angle for next call (controls the speed of the wave)
        angle += frequency;
        if (angle > 2 * PI) {
            angle -= 2 * PI;  // Keep angle within reasonable bounds
        }
        
        // Store the new value
        data[dataSize - 1] = (int8_t)newValue;
    }
    
    /**
     * Set the wave pattern
     * @param pattern The wave pattern to generate
     */
    void setPattern(WavePattern pattern) {
        this->pattern = pattern;
    }
    
    /**
     * Set wave parameters
     * @param frequency The frequency of the wave
     * @param amplitude The amplitude of the wave
     * @param phaseShift The phase shift of the wave
     */
    void setParameters(float frequency, float amplitude, float phaseShift = 0.0) {
        this->frequency = frequency;
        this->amplitude = amplitude;
        this->phaseShift = phaseShift;
    }
    
private:
    WavePattern pattern;
    float angle;
    float frequency;
    float amplitude;
    float phaseShift;
};

#endif // WAVE_DATA_PROVIDER_H
