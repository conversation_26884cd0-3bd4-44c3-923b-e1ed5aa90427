#ifndef GRAPH_MANAGER_H
#define GRAPH_MANAGER_H

#include <Arduino.h>
#include <vector>
#include "graphs/graph.h"
#include "graphs/data_provider.h"
#include "display/board_display.h"

/**
 * Structure to hold graph information
 */
struct GraphInfo {
    Graph* graph;       // Pointer to the graph
    int x;              // X position
    int y;              // Y position
    bool visible;       // Whether the graph is visible

    GraphInfo(Graph* graph, int x, int y, bool visible = true)
        : graph(graph), x(x), y(y), visible(visible) {}
};

/**
 * Class to manage a collection of graphs
 */
class GraphManager {
public:
    /**
     * Constructor
     * @param gfx Reference to the graphics object
     */
    GraphManager(BOARD_LGFX& gfx);

    /**
     * Destructor
     */
    ~GraphManager();

    /**
     * Add a graph with a data provider
     * @param x X position
     * @param y Y position
     * @param width Graph width
     * @param height Graph height
     * @param provider Data provider
     * @param takeOwnership Whether to take ownership of the provider
     * @param showBorder Whether to show a border
     * @param positiveColor Color for positive values
     * @param negativeColor Color for negative values
     * @param backgroundColor Background color
     * @param borderColor Border color
     * @return Index of the added graph
     */
    int addGraph(int x, int y, int width, int height,
                 DataProvider* provider,
                 bool takeOwnership = true,
                 bool showBorder = true,
                 uint16_t positiveColor = TFT_GREEN,
                 uint16_t negativeColor = TFT_RED,
                 uint16_t backgroundColor = TFT_BLACK,
                 uint16_t borderColor = TFT_DARKGREY);





    /**
     * Remove a graph
     * @param index Index of the graph to remove
     * @return True if successful, false otherwise
     */
    bool removeGraph(int index);

    /**
     * Set the visibility of a graph
     * @param index Index of the graph
     * @param visible Whether the graph is visible
     * @return True if successful, false otherwise
     */
    bool setGraphVisible(int index, bool visible);

    /**
     * Clear a graph's area on the screen
     * @param index Index of the graph
     * @param backgroundColor Background color to use for clearing
     * @return True if successful, false otherwise
     */
    bool clearGraphArea(int index, uint16_t backgroundColor = TFT_BLACK);

    /**
     * Set the position of a graph
     * @param index Index of the graph
     * @param x X position
     * @param y Y position
     * @param clearOld Whether to clear the old position
     * @return True if successful, false otherwise
     */
    bool setGraphPosition(int index, int x, int y, bool clearOld = true);

    /**
     * Get a graph by index
     * @param index Index of the graph
     * @return Pointer to the graph, or nullptr if not found
     */
    Graph* getGraph(int index);

    /**
     * Get the number of graphs
     * @return Number of graphs
     */
    int getGraphCount() const;

    /**
     * Update all graphs
     */
    void updateGraphs();

    /**
     * Draw all graphs
     */
    void drawGraphs();

    /**
     * Set the data provider for a graph
     * @param index Index of the graph
     * @param provider Data provider
     * @param takeOwnership Whether to take ownership of the provider
     * @return True if successful, false otherwise
     */
    bool setGraphDataProvider(int index, DataProvider* provider, bool takeOwnership = true);

private:
    BOARD_LGFX& gfx;
    std::vector<GraphInfo> graphs;
};

#endif // GRAPH_MANAGER_H
