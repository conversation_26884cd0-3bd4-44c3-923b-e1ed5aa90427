#ifndef DATA_PROVIDER_H
#define DATA_PROVIDER_H

#include <Arduino.h>

/**
 * Abstract base class for data providers
 * This class defines the interface for all data providers
 */
class DataProvider {
public:
    /**
     * Constructor
     * @param dataSize The number of data points to maintain
     * @param maxValue The maximum absolute value for data points
     */
    DataProvider(int dataSize, int maxValue) 
        : dataSize(dataSize), maxValue(maxValue) {
        data = new int8_t[dataSize];
        for (int i = 0; i < dataSize; i++) {
            data[i] = 0;
        }
    }
    
    /**
     * Destructor
     */
    virtual ~DataProvider() {
        if (data != nullptr) {
            delete[] data;
            data = nullptr;
        }
    }
    
    /**
     * Initialize the data provider
     * This method should be called after construction
     */
    virtual void initialize() = 0;
    
    /**
     * Update the data
     * This method should be called periodically to update the data
     */
    virtual void update() = 0;
    
    /**
     * Get the data array
     * @return Pointer to the data array
     */
    int8_t* getData() const {
        return data;
    }
    
    /**
     * Get the size of the data array
     * @return The size of the data array
     */
    int getDataSize() const {
        return dataSize;
    }
    
    /**
     * Get the maximum value
     * @return The maximum absolute value for data points
     */
    int getMaxValue() const {
        return maxValue;
    }
    
    /**
     * Set a specific data point
     * @param index The index of the data point
     * @param value The value to set
     */
    void setDataPoint(int index, int8_t value) {
        if (index >= 0 && index < dataSize) {
            data[index] = value;
        }
    }
    
protected:
    int8_t* data;
    int dataSize;
    int maxValue;
};

#endif // DATA_PROVIDER_H
