#ifndef HELLO_WORLD_APP_H
#define HELLO_WORLD_APP_H

#include "apps/app.h"
#include "display/ui_framework.h"

/**
 * A simple Hello World app
 */
class HelloWorldApp : public App {
public:
    /**
     * Constructor
     */
    HelloWorldApp();

    /**
     * Get the name of the app
     * @return Name of the app
     */
    std::string getName() const override { return "HelloWorldApp"; }

    /**
     * Destructor
     */
    ~HelloWorldApp() override;

    /**
     * Initialize the app
     */
    void setup() override;

    /**
     * Update the app state
     */
    void update() override;

    /**
     * Called when the app is activated
     */
    void onActivate() override;

    /**
     * Called when the app is deactivated
     */
    void onDeactivate() override;

protected:
    /**
     * Handle button click events
     * @param buttonId Button identifier
     * @param clickCount Number of clicks
     */
    void handleButtonClick(ButtonId buttonId, int clickCount) override;

    /**
     * Handle button long press start events
     * @param buttonId Button identifier
     */
    void handleButtonLongPressStart(ButtonId buttonId) override;

    /**
     * Handle button long press events
     * @param buttonId Button identifier
     */
    void handleButtonLongPress(ButtonId buttonId) override;

    /**
     * Handle button long press end events
     * @param buttonId Button identifier
     */
    void handleButtonLongPressEnd(ButtonId buttonId) override;

public:
    /**
     * Set the app switch callback
     * @param callback Function to call when the app wants to switch to another app
     */
    void setOnAppSwitchCallback(std::function<void(const std::string&)> callback) {
        onAppSwitchCallback = callback;
    }

private:
    UIManager* uiManager;
    Label* titleLabel;
    Label* infoLabel;
    Label* counterLabel;
    int counter;
    std::function<void(const std::string&)> onAppSwitchCallback;
};

#endif // HELLO_WORLD_APP_H
