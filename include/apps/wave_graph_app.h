#ifndef WAVE_GRAPH_APP_H
#define WAVE_GRAPH_APP_H

#include "apps/app.h"
#include "graphs/wave_data_provider.h"
#include "graphs/graph_manager.h"

/**
 * App that displays and manipulates wave graphs
 */
class WaveGraphApp : public App {
public:
    /**
     * Constructor
     */
    WaveGraphApp();

    /**
     * Get the name of the app
     * @return Name of the app
     */
    std::string getName() const override { return "WaveGraphApp"; }

    /**
     * Destructor
     */
    ~WaveGraphApp() override;

    /**
     * Initialize the app
     */
    void setup() override;

    /**
     * Update the app state
     */
    void update() override;

    /**
     * Called when the app is activated
     */
    void onActivate() override;

    /**
     * Called when the app is deactivated
     */
    void onDeactivate() override;

protected:
    /**
     * Handle button click events
     * @param buttonId Button identifier
     * @param clickCount Number of clicks
     */
    void handleButtonClick(ButtonId buttonId, int clickCount) override;

    /**
     * Handle button long press start events
     * @param buttonId Button identifier
     */
    void handleButtonLongPressStart(ButtonId buttonId) override;

    /**
     * Handle button long press events
     * @param buttonId Button identifier
     */
    void handleButtonLongPress(ButtonId buttonId) override;

    /**
     * Handle button long press end events
     * @param buttonId Button identifier
     */
    void handleButtonLongPressEnd(ButtonId buttonId) override;

public:
    /**
     * Set the app switch callback
     * @param callback Function to call when the app wants to switch to another app
     */
    void setOnAppSwitchCallback(std::function<void(const std::string&)> callback) {
        onAppSwitchCallback = callback;
    }

private:
    GraphManager* graphManager;
    int graphCount;
    int patternIndex;
    bool useRealData;
    int graphYPosition;
    bool graph1Visible;
    bool graphUpdatesPaused;
    std::function<void(const std::string&)> onAppSwitchCallback;

    /**
     * Add a graph with a wave data provider
     * @param x X position
     * @param y Y position
     * @param width Graph width
     * @param height Graph height
     * @param pattern Wave pattern
     * @param showBorder Whether to show a border
     * @return Index of the added graph
     */
    int addWaveGraph(int x, int y, int width, int height,
                     WaveDataProvider::WavePattern pattern = WaveDataProvider::SINE_WAVE,
                     bool showBorder = true);

    /**
     * Add a graph with a real data provider
     * @param x X position
     * @param y Y position
     * @param width Graph width
     * @param height Graph height
     * @param showBorder Whether to show a border
     * @return Index of the added graph
     */
    int addRealDataGraph(int x, int y, int width, int height, bool showBorder = true);

    /**
     * Remove a graph
     * @param index Index of the graph to remove
     * @return True if successful, false otherwise
     */
    bool removeGraph(int index);

    /**
     * Set the visibility of a graph
     * @param index Index of the graph
     * @param visible Whether the graph is visible
     * @return True if successful, false otherwise
     */
    bool setGraphVisible(int index, bool visible);

    /**
     * Clear a graph's area on the screen
     * @param index Index of the graph
     * @param backgroundColor Background color to use for clearing
     * @return True if successful, false otherwise
     */
    bool clearGraphArea(int index, uint16_t backgroundColor = TFT_BLACK);

    /**
     * Set the position of a graph
     * @param index Index of the graph
     * @param x X position
     * @param y Y position
     * @param clearOld Whether to clear the old position
     * @return True if successful, false otherwise
     */
    bool setGraphPosition(int index, int x, int y, bool clearOld = true);

    /**
     * Switch a graph to use a wave data provider
     * @param index Index of the graph
     * @param pattern Wave pattern
     * @return True if successful, false otherwise
     */
    bool switchToWaveData(int index, WaveDataProvider::WavePattern pattern);

    /**
     * Switch a graph to use a real data provider
     * @param index Index of the graph
     * @return True if successful, false otherwise
     */
    bool switchToRealData(int index);
};

#endif // WAVE_GRAPH_APP_H
