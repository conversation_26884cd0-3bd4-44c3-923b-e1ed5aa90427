#ifndef APP_H
#define APP_H

#include "display/display.h"
#include "buttons/app_button_handler.h"
#include <vector>
#include <map>
#include <string>

/**
 * Button identifiers for semantic button handling
 */
enum class ButtonId {
    BUTTON1,
    BUTTON2,
    // Add more buttons as needed
};

/**
 * Abstract base class for applications
 * Each app can have its own display content and button handling
 */
class App {
public:
    /**
     * Constructor
     */
    App() : display(nullptr) {}

    /**
     * Virtual destructor
     */
    virtual ~App() {}

    /**
     * Initialize the app
     */
    virtual void setup() = 0;

    /**
     * Update the app state
     * Called periodically from the main loop
     */
    virtual void update() = 0;

    /**
     * Called when the app is activated
     */
    virtual void onActivate() {
        // Default implementation does nothing
    }

    /**
     * Called when the app is deactivated
     */
    virtual void onDeactivate() {
        // Default implementation does nothing
    }

    /**
     * Set the display
     * @param display Pointer to the display
     */
    void setDisplay(Display* display) { this->display = display; }

    /**
     * Get the display
     * @return Pointer to the display
     */
    Display* getDisplay() const { return display; }

    /**
     * Switch to another app
     * @param appName Name of the app to switch to
     * @return true if successful, false otherwise
     */
    bool switchToApp(const std::string& appName);

    /**
     * Set the app switch callback
     * @param callback Function to call when the app wants to switch to another app
     */
    void setOnAppSwitchCallback(std::function<bool(const std::string&)> callback) {
        onAppSwitchCallback = callback;
    }

    /**
     * Get the name of the app
     * @return Name of the app
     */
    virtual std::string getName() const = 0;

    /**
     * Register button handlers with this app
     * @param buttonHandlers Map of button handlers to register
     */
    void registerButtonHandlers(const std::map<ButtonId, AppButtonHandler*>& buttonHandlers) {
        this->buttonHandlers = buttonHandlers;

        // Set up callbacks for each button handler
        for (auto& pair : buttonHandlers) {
            ButtonId id = pair.first;
            AppButtonHandler* handler = pair.second;

            handler->setOnClickCallback([this, id](int pinNumber, int clickCount) {
                this->handleButtonClick(id, clickCount);
            });

            handler->setOnLongPressStartCallback([this, id](int pinNumber) {
                this->handleButtonLongPressStart(id);
            });

            handler->setOnLongPressCallback([this, id](int pinNumber) {
                this->handleButtonLongPress(id);
            });

            handler->setOnLongPressEndCallback([this, id](int pinNumber) {
                this->handleButtonLongPressEnd(id);
            });
        }
    }

    /**
     * For backward compatibility with vector-based registration
     */
    void registerButtonHandlers(const std::vector<AppButtonHandler*>& buttonHandlers) {
        // Map PIN_BUTTON_1 to ButtonId::BUTTON1 and PIN_BUTTON_2 to ButtonId::BUTTON2
        std::map<ButtonId, AppButtonHandler*> buttonMap;

        for (auto handler : buttonHandlers) {
            int pinNumber = handler->getPinNumber();
            if (pinNumber == PIN_BUTTON_1) {
                buttonMap[ButtonId::BUTTON1] = handler;
            } else if (pinNumber == PIN_BUTTON_2) {
                buttonMap[ButtonId::BUTTON2] = handler;
            }
        }

        registerButtonHandlers(buttonMap);
    }

protected:
    Display* display;
    std::map<ButtonId, AppButtonHandler*> buttonHandlers;
    std::function<bool(const std::string&)> onAppSwitchCallback;

    /**
     * Handle button click events
     * @param buttonId Button identifier
     * @param clickCount Number of clicks
     */
    virtual void handleButtonClick(ButtonId buttonId, int clickCount) = 0;

    /**
     * Handle button long press start events
     * @param buttonId Button identifier
     */
    virtual void handleButtonLongPressStart(ButtonId buttonId) = 0;

    /**
     * Handle button long press events
     * @param buttonId Button identifier
     */
    virtual void handleButtonLongPress(ButtonId buttonId) = 0;

    /**
     * Handle button long press end events
     * @param buttonId Button identifier
     */
    virtual void handleButtonLongPressEnd(ButtonId buttonId) = 0;

    // Legacy handlers that map pin numbers to button IDs
    void handleButtonClick(int pinNumber, int clickCount) {
        ButtonId buttonId = pinToButtonId(pinNumber);
        handleButtonClick(buttonId, clickCount);
    }

    void handleButtonLongPressStart(int pinNumber) {
        ButtonId buttonId = pinToButtonId(pinNumber);
        handleButtonLongPressStart(buttonId);
    }

    void handleButtonLongPress(int pinNumber) {
        ButtonId buttonId = pinToButtonId(pinNumber);
        handleButtonLongPress(buttonId);
    }

    void handleButtonLongPressEnd(int pinNumber) {
        ButtonId buttonId = pinToButtonId(pinNumber);
        handleButtonLongPressEnd(buttonId);
    }

    /**
     * Convert a pin number to a button ID
     * @param pinNumber Pin number
     * @return Button ID
     */
    ButtonId pinToButtonId(int pinNumber) {
        if (pinNumber == PIN_BUTTON_1) {
            return ButtonId::BUTTON1;
        } else if (pinNumber == PIN_BUTTON_2) {
            return ButtonId::BUTTON2;
        }
        // Default to BUTTON1 if pin number is unknown
        return ButtonId::BUTTON1;
    }
};

#endif // APP_H
