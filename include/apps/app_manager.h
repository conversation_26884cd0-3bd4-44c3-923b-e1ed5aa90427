#ifndef APP_MANAGER_H
#define APP_MANAGER_H

#include "apps/app.h"
#include "buttons/app_button_handler.h"
#include <vector>
#include <map>
#include <string>

/**
 * Manages the current app and handles app switching
 */
class AppManager {
public:
    /**
     * Constructor
     * @param display Pointer to the display
     */
    AppManager(Display* display);

    /**
     * Destructor
     */
    ~AppManager();

    /**
     * Set the current app
     * @param app Pointer to the app to set as current
     * @param takeOwnership Whether to take ownership of the app (delete it when done)
     */
    void setCurrentApp(App* app, bool takeOwnership = true);

    // This method is implemented in the AppRegistry interface

    /**
     * Register a button handler
     * @param buttonId Button identifier
     * @param handler Pointer to the button handler to register
     */
    void registerButtonHandler(ButtonId buttonId, AppButtonHandler* handler);

    /**
     * For backward compatibility
     * @param handler Pointer to the button handler to register
     */
    void registerButtonHandler(AppButtonHandler* handler);

    /**
     * Update the current app
     * Called from the main loop
     */
    void update();

    /**
     * Register an app with the registry
     * @param name Name of the app
     * @param app Pointer to the app
     */
    void registerApp(const std::string& name, App* app);

    /**
     * Switch to a different app by name
     * @param name Name of the app to switch to
     * @return true if the app was found and switched to, false otherwise
     */
    bool switchToApp(const std::string& name);

    /**
     * Get an app by name
     * @param name Name of the app to get
     * @return Pointer to the app, or nullptr if not found
     */
    App* getApp(const std::string& name);

    /**
     * Get the current app
     * @return Pointer to the current app
     */
    App* getCurrentApp() const { return currentApp; }

    /**
     * Get all registered apps
     * @return Map of app names to app pointers
     */
    const std::map<std::string, App*>& getApps() const { return apps; }

    /**
     * Get the display
     * @return Pointer to the display
     */
    Display* getDisplay() const { return display; }

private:
    Display* display;
    App* currentApp;
    App* previousApp;
    std::map<ButtonId, AppButtonHandler*> buttonHandlers;
    std::map<std::string, App*> apps;
};

#endif // APP_MANAGER_H
