#ifndef CONFIG_H
#define CONFIG_H

#include <ArduinoJson.h>

class Config
{
private:
    const char *filename;
    JsonDocument config;

    void load(void);

public:
    Config(const char *filename = "/config.json");

    void save(void);

    const char *getWifiSsid(void) { return this->config["wifi.ssid"]; }
    const char *getWhatsappNumber(void) { return this->config["whatsapp.number"]; }
    const char *getWhatsappApiKey(void) { return this->config["whatsapp.apikey"]; }
};

#endif // CONFIG_H