#ifndef ESP32_4848S040_H
#define ESP32_4848S040_H

/*ESP32-4848S040 with GT911 touch and ST7701 display via RGB interface*/

// Display pins for RGB interface
#define PIN_LCD_DE     18  // Data Enable
#define PIN_LCD_VSYNC  17  // Vertical Sync
#define PIN_LCD_HSYNC  16  // Horizontal Sync
#define PIN_LCD_PCLK   21  // Pixel Clock

// RGB data pins
// Red pins
#define PIN_LCD_R0     11
#define PIN_LCD_R1     12
#define PIN_LCD_R2     13
#define PIN_LCD_R3     14
#define PIN_LCD_R4     0

// Green pins
#define PIN_LCD_G0     8
#define PIN_LCD_G1     20
#define PIN_LCD_G2     3
#define PIN_LCD_G3     46
#define PIN_LCD_G4     9
#define PIN_LCD_G5     10

// Blue pins
#define PIN_LCD_B0     4
#define PIN_LCD_B1     5
#define PIN_LCD_B2     6
#define PIN_LCD_B3     7
#define PIN_LCD_B4     15

// Display initialization SPI pins
#define PIN_LCD_CS     39  // Chip Select
#define PIN_LCD_SCK    48  // Clock
#define PIN_LCD_SDA    47  // Data

// Backlight control
#define PIN_LCD_BL     38

// Touch screen pins (GT911)
#define PIN_TOUCH_SDA  19
#define PIN_TOUCH_SCL  45
#define PIN_TOUCH_INT  -1  // Not directly connected
#define PIN_TOUCH_RST  -1  // Not directly connected

// SD Card pins
#define PIN_SD_CS      42  // Chip Select
#define PIN_SD_MOSI    47  // Data In (shared with display SPI)
#define PIN_SD_MISO    41  // Data Out
#define PIN_SD_SCK     48  // Clock (shared with display SPI)

// Buttons
#define PIN_BUTTON_1   21
#define PIN_BUTTON_2   22

// LDR for auto-brightness
#define PIN_LDR        1

// Power control
#define PIN_POWER_ON   -1  // Set to actual pin if available

// I2C bus
#define PIN_I2C_SDA    19
#define PIN_I2C_SCL    45

#endif // ESP32_4848S040C_H
