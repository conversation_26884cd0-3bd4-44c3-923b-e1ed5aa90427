#ifndef DISPLAY_H
#define DISPLAY_H

#include "display/display_driver.h"
#include "display/system_ui.h"

/**
 * Handles high-level display operations
 */
class Display
{
private:
    DisplayDriver* displayDriver;
    SystemUI* systemUI;
    TaskHandle_t threadHandle;

    void loop();
    static void thread(void *context);

public:
    /**
     * Constructor
     */
    Display();

    /**
     * Destructor
     */
    ~Display();

    /**
     * Initialize the display
     */
    void setup();

    /**
     * Configure the light-dependent resistor for auto-dimming
     * @param ldrPin Pin connected to the light-dependent resistor
     */
    void configureLdr(uint8_t ldrPin);

    /**
     * Clear the display
     */
    void clear();

    /**
     * Print formatted text to the display
     * @param format Format string
     * @param ... Variable arguments
     */
    void log(const char *format, ...);

    /**
     * Get the display driver
     * @return Pointer to the display driver
     */
    DisplayDriver* getDisplayDriver() { return displayDriver; }

    /**
     * Get the system UI
     * @return Pointer to the system UI
     */
    SystemUI* getSystemUI() { return systemUI; }

    /**
     * Get the LGFX object for direct drawing
     * @return Reference to the BOARD_LGFX object
     */
    BOARD_LGFX& getGfx();
};

#endif // DISPLAY_H
