#ifndef DISPLAY_DRIVER_H
#define DISPLAY_DRIVER_H

#include <Arduino.h>
#include "display/board_display.h"

// Include the appropriate board header based on the board type
#if defined(LILYGO_T_DISPLAY_S3)
    #include "esp32s3.h"
#elif defined(ESP32_4848S040)
    #include "esp32_4848s040.h"
#endif

#define LCD_BRIGHTNESS 127

/**
 * Handles low-level display operations
 */
class DisplayDriver {
public:
    /**
     * Constructor
     */
    DisplayDriver();

    /**
     * Destructor
     */
    ~DisplayDriver();

    /**
     * Initialize the display
     */
    void setup();

    /**
     * Configure the light-dependent resistor for auto-dimming
     * @param ldrPin Pin connected to the light-dependent resistor
     */
    void configureLdr(uint8_t ldrPin);

    /**
     * Clear the display
     */
    void clear();

    /**
     * Print formatted text to the display
     * @param format Format string
     * @param ... Variable arguments
     */
    void log(const char *buffer);

    /**
     * Update the display brightness based on ambient light
     */
    void updateBrightness();

    /**
     * Get the LGFX object for direct drawing
     * @return Reference to the LGFX object
     */
    BOARD_LGFX& getGfx() { return gfx; }

private:
    BOARD_LGFX gfx;
    int8_t ldrPin;
};

#endif // DISPLAY_DRIVER_H
