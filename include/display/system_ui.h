#ifndef SYSTEM_UI_H
#define SYSTEM_UI_H

#include "display/display_driver.h"

/**
 * Handles system-level UI elements
 */
class SystemUI {
public:
    /**
     * Constructor
     * @param displayDriver Pointer to the display driver
     */
    SystemUI(DisplayDriver* displayDriver);
    
    /**
     * Destructor
     */
    ~SystemUI();
    
    /**
     * Initialize the system UI
     */
    void setup();
    
    /**
     * Update the system UI
     */
    void update();
    
    /**
     * Update the signal strength indicator
     */
    void updateSignalStrength();

private:
    DisplayDriver* displayDriver;
    unsigned long lastUpdateTime;
};

#endif // SYSTEM_UI_H
