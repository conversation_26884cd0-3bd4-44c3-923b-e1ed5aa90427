#ifndef UI_FRAMEWORK_H
#define UI_FRAMEWORK_H

#include "display/display.h"
#include <string>
#include <vector>

/**
 * UI element types
 */
enum class UIElementType {
    LABEL,
    BUTTON,
    GRAPH
};

/**
 * Base class for UI elements
 */
class UIElement {
public:
    UIElement(int x, int y, int width, int height) 
        : x(x), y(y), width(width), height(height), visible(true) {}
    
    virtual ~UIElement() {}
    
    virtual void draw(Display* display) = 0;
    
    void setVisible(bool visible) { this->visible = visible; }
    bool isVisible() const { return visible; }
    
    int getX() const { return x; }
    int getY() const { return y; }
    int getWidth() const { return width; }
    int getHeight() const { return height; }
    
    void setPosition(int x, int y) {
        this->x = x;
        this->y = y;
    }
    
    virtual UIElementType getType() const = 0;

protected:
    int x;
    int y;
    int width;
    int height;
    bool visible;
};

/**
 * Label UI element
 */
class Label : public UIElement {
public:
    Label(int x, int y, const std::string& text, uint16_t color = TFT_WHITE, uint8_t size = 1)
        : UIElement(x, y, 0, 0), text(text), color(color), size(size) {}
    
    void draw(Display* display) override {
        if (!visible) return;
        
        display->getGfx().setTextColor(color);
        display->getGfx().setTextSize(size);
        display->getGfx().setCursor(x, y);
        display->getGfx().print(text.c_str());
    }
    
    void setText(const std::string& text) { this->text = text; }
    std::string getText() const { return text; }
    
    void setColor(uint16_t color) { this->color = color; }
    uint16_t getColor() const { return color; }
    
    void setSize(uint8_t size) { this->size = size; }
    uint8_t getSize() const { return size; }
    
    UIElementType getType() const override { return UIElementType::LABEL; }

private:
    std::string text;
    uint16_t color;
    uint8_t size;
};

/**
 * UI manager class
 */
class UIManager {
public:
    UIManager(Display* display) : display(display) {}
    
    ~UIManager() {
        // Clean up all UI elements
        for (auto element : elements) {
            delete element;
        }
        elements.clear();
    }
    
    void addElement(UIElement* element) {
        elements.push_back(element);
    }
    
    void removeElement(UIElement* element) {
        auto it = std::find(elements.begin(), elements.end(), element);
        if (it != elements.end()) {
            delete *it;
            elements.erase(it);
        }
    }
    
    void clearElements() {
        for (auto element : elements) {
            delete element;
        }
        elements.clear();
    }
    
    void drawElements() {
        for (auto element : elements) {
            element->draw(display);
        }
    }
    
    Display* getDisplay() const { return display; }

private:
    Display* display;
    std::vector<UIElement*> elements;
};

#endif // UI_FRAMEWORK_H
