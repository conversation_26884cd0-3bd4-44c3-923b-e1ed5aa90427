#ifndef APP_BUTTON_HANDLER_H
#define APP_BUTTON_HANDLER_H

#if defined(LILYGO_T_DISPLAY_S3)
#include <ESP32ButtonHandler.h>
#include <functional>

/**
 * Button handler that uses callbacks for button events
 */
class AppButtonHandler : public ESP32ButtonHandler
{
public:
    // Callback function types
    using ClickCallback = std::function<void(int pinNumber, int clickCount)>;
    using PressCallback = std::function<void(int pinNumber)>;

    /**
     * Constructor
     * @param pinNumber Button pin number
     * @param activeLow Whether the button is active low
     * @param pullupActive Whether to enable the internal pullup resistor
     */
    AppButtonHandler(int pinNumber, bool activeLow = true, bool pullupActive = true)
        : ESP32ButtonHandler(pinNumber, activeLow, pullupActive), pinNumber(pinNumber)
    {
        // Set up callbacks to forward events to our simplified interface
        ESP32ButtonHandler::setOnClickCallback([this, pinNumber](ESP32ButtonHandler *handler, int clickCount)
                                               {
                if (onClickCallback) {
                    onClickCallback(pinNumber, clickCount);
                } });

        ESP32ButtonHandler::setOnLongPressStartCallback([this, pinNumber](ESP32ButtonHandler *handler)
                                                        {
                if (onLongPressStartCallback) {
                    onLongPressStartCallback(pinNumber);
                } });

        ESP32ButtonHandler::setOnLongPressCallback([this, pinNumber](ESP32ButtonHandler *handler)
                                                   {
                if (onLongPressCallback) {
                    onLongPressCallback(pinNumber);
                } });

        ESP32ButtonHandler::setOnLongPressEndCallback([this, pinNumber](ESP32ButtonHandler *handler)
                                                      {
                if (onLongPressEndCallback) {
                    onLongPressEndCallback(pinNumber);
                } });
    }

    /**
     * Get the pin number for this button handler
     * @return The pin number
     */
    int getPinNumber() const { return pinNumber; }

    /**
     * Set the callback for click events
     * @param callback Function to call when the button is clicked
     */
    void setOnClickCallback(ClickCallback callback) { onClickCallback = callback; }

    /**
     * Set the callback for long press start events
     * @param callback Function to call when a long press starts
     */
    void setOnLongPressStartCallback(PressCallback callback) { onLongPressStartCallback = callback; }

    /**
     * Set the callback for long press events
     * @param callback Function to call during a long press
     */
    void setOnLongPressCallback(PressCallback callback) { onLongPressCallback = callback; }

    /**
     * Set the callback for long press end events
     * @param callback Function to call when a long press ends
     */
    void setOnLongPressEndCallback(PressCallback callback) { onLongPressEndCallback = callback; }

private:
    int pinNumber;
    ClickCallback onClickCallback;
    PressCallback onLongPressStartCallback;
    PressCallback onLongPressCallback;
    PressCallback onLongPressEndCallback;
};

#elif defined(ESP32_4848S040)
#include "esp32_4848s040.h"
class AppButtonHandler
{
public:
    // Callback function types
    using ClickCallback = std::function<void(int pinNumber, int clickCount)>;
    using PressCallback = std::function<void(int pinNumber)>;

    /**
     * Constructor
     * @param pinNumber Button pin number
     * @param activeLow Whether the button is active low
     * @param pullupActive Whether to enable the internal pullup resistor
     */
    AppButtonHandler(int pinNumber, bool activeLow = true, bool pullupActive = true)
    {
    }

    /**
     * Get the pin number for this button handler
     * @return The pin number
     */
    int getPinNumber() const { return 0; }

    /**
     * Set the callback for click events
     * @param callback Function to call when the button is clicked
     */
    void setOnClickCallback(ClickCallback callback) { }

    /**
     * Set the callback for long press start events
     * @param callback Function to call when a long press starts
     */
    void setOnLongPressStartCallback(PressCallback callback) { }

    /**
     * Set the callback for long press events
     * @param callback Function to call during a long press
     */
    void setOnLongPressCallback(PressCallback callback) { }

    /**
     * Set the callback for long press end events
     * @param callback Function to call when a long press ends
     */
    void setOnLongPressEndCallback(PressCallback callback) { }
};
#endif

#endif // APP_BUTTON_HANDLER_H
