[platformio]
; default_envs = lilygo-t-display-s3
default_envs = esp32-4848s040

[env]
platform = espressif32
framework = arduino
monitor_speed = 115200

[env:lilygo-t-display-s3]
board = lilygo-t-display-s3
build_flags = 
	-D LILYGO_T_DISPLAY_S3
lib_deps = 
	lovyan03/LovyanGFX@^1.1.9
	olikraus/U8g2@^2.36.5
	hafidh/Callmebot ESP32@^2.0.0
	bblanchon/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@^7.3.1
	yawom/ESP32ButtonHandler@^2.0.0

[env:esp32-4848s040]
board = esp32-4848s040
upload_speed = 460800
build_flags = 
	-D ESP32_4848S040
lib_deps =
	lovyan03/LovyanGFX@^1.1.9
	olikraus/U8g2@^2.36.5
	hafidh/Callmebot ESP32@^2.0.0
	bblanchon/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@^7.3.1
