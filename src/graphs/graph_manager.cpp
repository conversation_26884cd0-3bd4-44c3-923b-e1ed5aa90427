#include "graphs/graph_manager.h"

GraphManager::GraphManager(BOARD_LGFX& gfx) : gfx(gfx) {
}

GraphManager::~GraphManager() {
    // Clean up all graphs
    for (auto& info : graphs) {
        if (info.graph) {
            delete info.graph;
        }
    }
    graphs.clear();
}

int GraphManager::addGraph(int x, int y, int width, int height,
                 DataProvider* provider,
                 bool takeOwnership,
                 bool showBorder,
                 uint16_t positiveColor,
                 uint16_t negativeColor,
                 uint16_t backgroundColor,
                 uint16_t borderColor) {

    // Create the graph
    Graph* graph = new Graph(gfx, width, height, provider, takeOwnership, showBorder);
    graph->setColors(positiveColor, negativeColor, backgroundColor, borderColor);

    // Add to our collection
    graphs.push_back(GraphInfo(graph, x, y));

    // Return the index
    return graphs.size() - 1;
}

bool GraphManager::removeGraph(int index) {
    if (index < 0 || index >= graphs.size()) {
        return false;
    }

    // Delete the graph
    if (graphs[index].graph) {
        delete graphs[index].graph;
    }

    // Remove from the vector
    graphs.erase(graphs.begin() + index);

    return true;
}

bool GraphManager::setGraphVisible(int index, bool visible) {
    if (index < 0 || index >= graphs.size()) {
        return false;
    }

    graphs[index].visible = visible;
    return true;
}

bool GraphManager::clearGraphArea(int index, uint16_t backgroundColor) {
    if (index < 0 || index >= graphs.size() || !graphs[index].graph) {
        return false;
    }

    Graph* graph = graphs[index].graph;
    int x = graphs[index].x;
    int y = graphs[index].y;
    int width = graph->getWidth();
    int height = graph->getHeight();

    // Clear the graph area
    gfx.fillRect(x, y, width, height*2, backgroundColor);

    return true;
}

bool GraphManager::setGraphPosition(int index, int x, int y, bool clearOld) {
    if (index < 0 || index >= graphs.size()) {
        return false;
    }

    // Clear the old position if requested
    if (clearOld && graphs[index].graph && graphs[index].visible) {
        clearGraphArea(index);
    }

    // Update the position
    graphs[index].x = x;
    graphs[index].y = y;

    return true;
}

Graph* GraphManager::getGraph(int index) {
    if (index < 0 || index >= graphs.size()) {
        return nullptr;
    }

    return graphs[index].graph;
}

int GraphManager::getGraphCount() const {
    return graphs.size();
}

void GraphManager::updateGraphs() {
    for (auto& info : graphs) {
        if (info.graph && info.visible) {
            info.graph->updateData();
        }
    }
}

void GraphManager::drawGraphs() {
    for (auto& info : graphs) {
        if (info.graph && info.visible) {
            info.graph->draw(info.x, info.y);
        }
    }
}

bool GraphManager::setGraphDataProvider(int index, DataProvider* provider, bool takeOwnership) {
    if (index < 0 || index >= graphs.size() || !graphs[index].graph) {
        return false;
    }

    Graph* graph = graphs[index].graph;

    // Set the new data provider
    graph->setDataProvider(provider, takeOwnership);

    return true;
}
