#include "graphs/graph.h"

Graph::Graph(BOARD_LGFX& gfx, int width, int height, DataProvider* dataProvider, bool ownsDataProvider, bool showBorder)
    : gfx(gfx), width(width), height(height), dataProvider(dataProvider), ownsDataProvider(ownsDataProvider), showBorder(showBorder)
{
    // Set default colors
    this->positiveColor = TFT_GREEN;
    this->negativeColor = TFT_RED;
    this->backgroundColor = TFT_BLACK;
    this->borderColor = TFT_DARKGREY;

    // Initialize the data provider
    if (dataProvider) {
        dataProvider->initialize();
    }
}

Graph::~Graph()
{
    // Free allocated memory if we own the data provider
    if (ownsDataProvider && dataProvider) {
        delete dataProvider;
        dataProvider = nullptr;
    }
}

void Graph::setColors(uint16_t positiveColor, uint16_t negativeColor, uint16_t backgroundColor, uint16_t borderColor)
{
    this->positiveColor = positiveColor;
    this->negativeColor = negativeColor;
    this->backgroundColor = backgroundColor;
    this->borderColor = borderColor;
}

void Graph::setBorder(bool showBorder)
{
    this->showBorder = showBorder;
}

void Graph::draw(int x, int y)
{
    // Check if we have a data provider
    if (!dataProvider) {
        return;
    }

    // Get the data from the provider
    int8_t* data = dataProvider->getData();
    int dataSize = dataProvider->getDataSize();

    // Clear the graph area
    gfx.fillRect(x, y, width, height*2, backgroundColor);

    // Draw the graph data
    for (int i = 0; i < width && i < dataSize; i++) {
        int h = data[i];
        if (h < 0) {
            // Draw negative values below the center line
            gfx.drawFastVLine(x + i, y + height, -h, negativeColor);
        } else {
            // Draw positive values above the center line
            gfx.drawFastVLine(x + i, y + height - h, h, positiveColor);
        }
    }

    // Draw border if enabled
    if (showBorder) {
        // Draw a rectangle around the graph
        gfx.drawRect(x, y, width, height*2, borderColor);
    }
}

void Graph::updateData()
{
    // Check if we have a data provider
    if (dataProvider) {
        dataProvider->update();
    }
}

void Graph::setDataProvider(DataProvider* dataProvider, bool ownsDataProvider)
{
    // Clean up old data provider if we own it
    if (this->ownsDataProvider && this->dataProvider) {
        delete this->dataProvider;
    }

    // Set the new data provider
    this->dataProvider = dataProvider;
    this->ownsDataProvider = ownsDataProvider;

    // Initialize the new data provider
    if (dataProvider) {
        dataProvider->initialize();
    }
}

DataProvider* Graph::getDataProvider() const
{
    return dataProvider;
}

void Graph::setWavePattern(WaveDataProvider::WavePattern pattern)
{
    // Since we can't use dynamic_cast with -fno-rtti, we'll check if the provider
    // is a WaveDataProvider by checking if it has the right methods
    if (dataProvider) {
        // Try to cast to WaveDataProvider* and call the method
        // This is a bit of a hack, but it should work if the dataProvider is actually a WaveDataProvider
        WaveDataProvider* waveProvider = static_cast<WaveDataProvider*>(dataProvider);
        waveProvider->setPattern(pattern);
    }
}

void Graph::setWaveParameters(float frequency, float amplitude, float phaseShift)
{
    // Since we can't use dynamic_cast with -fno-rtti, we'll check if the provider
    // is a WaveDataProvider by checking if it has the right methods
    if (dataProvider) {
        // Try to cast to WaveDataProvider* and call the method
        // This is a bit of a hack, but it should work if the dataProvider is actually a WaveDataProvider
        WaveDataProvider* waveProvider = static_cast<WaveDataProvider*>(dataProvider);
        waveProvider->setParameters(frequency, amplitude, phaseShift);
    }
}
