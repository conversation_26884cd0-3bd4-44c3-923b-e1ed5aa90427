#include "display/display_driver.h"

DisplayDriver::DisplayDriver() : ldrPin(-1)
{
}

DisplayDriver::~DisplayDriver()
{
}

void DisplayDriver::setup()
{
#if defined(LILYGO_T_DISPLAY_S3)
    pinMode(PIN_POWER_ON, OUTPUT);
    digitalWrite(PIN_POWER_ON, HIGH);
#endif

    pinMode(PIN_LCD_BL, ANALOG);
    analogWrite(PIN_LCD_BL, 64);

    gfx.init();
    gfx.setRotation(0);
    gfx.fillScreen(TFT_BLACK);
    gfx.setTextColor(TFT_WHITE);
    gfx.setCursor(0, 0);

    this->clear();
}

void DisplayDriver::configureLdr(uint8_t ldrPin)
{
    this->ldrPin = ldrPin;
    if (this->ldrPin != -1) {
        analogSetPinAttenuation(this->ldrPin, ADC_11db);
        Serial.printf("LDR configured on pin %d\n", this->ldrPin);
    }
}

void DisplayDriver::clear(void)
{
    gfx.fillScreen(TFT_BLACK);
    gfx.setCursor(0, 0);
}

void DisplayDriver::log(const char *buffer)
{
    gfx.print(buffer);
    Serial.print(buffer);
}

void DisplayDriver::updateBrightness()
{
    if (ldrPin != -1)
    {
        uint16_t ldr = analogRead(ldrPin);
        uint32_t lcd_brightness;

        lcd_brightness = map(ldr, 0, 4095, 0, 255);

        #ifdef DEBUG
        // Add debug logging
        static uint32_t lastLogTime = 0;
        uint32_t currentTime = millis();
        if (currentTime - lastLogTime > 5000) { // Log every 5 seconds
            Serial.printf("LDR value: %d, Brightness: %d\n", ldr, lcd_brightness);
            lastLogTime = currentTime;
        }
        #endif // DEBUG

        ledcWrite(0, lcd_brightness);
    }
}
