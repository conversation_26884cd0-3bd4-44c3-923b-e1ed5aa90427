#include "display/display.h"
#include <stdarg.h>

Display::Display()
    : displayDriver(nullptr), systemUI(nullptr), threadHandle(nullptr)
{
}

Display::~Display()
{
    if (threadHandle != nullptr)
    {
        vTaskDelete(threadHandle);
    }

    if (systemUI != nullptr)
    {
        delete systemUI;
    }

    if (displayDriver != nullptr)
    {
        delete displayDriver;
    }
}

void Display::setup()
{
    // Create and initialize the display driver
    displayDriver = new DisplayDriver();
    displayDriver->setup();

    // Create and initialize the system UI
    systemUI = new SystemUI(displayDriver);
    systemUI->setup();

    // Clear the display
    this->clear();

    // Create a thread for the display
    xTaskCreatePinnedToCore(thread, "Display", 4096, this, 1, &threadHandle, 1);
}

void Display::configureLdr(uint8_t ldrPin)
{
    if (displayDriver) {
        displayDriver->configureLdr(ldrPin);
    }
}

void Display::clear()
{
    if (displayDriver)
    {
        displayDriver->clear();
    }
}

void Display::log(const char *format, ...)
{
    char buffer[256];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    if (displayDriver)
    {
        displayDriver->log(buffer);
    }
}

void Display::loop()
{
    if (systemUI)
    {
        systemUI->update();
    }

    if (displayDriver)
    {
        // displayDriver->updateBrightness();
    }
}

void Display::thread(void *context)
{
    Display *display = (Display *)context;

    while (true)
    {
        display->loop();
        // Run more frequently for more responsive brightness adjustment
        vTaskDelay(250 / portTICK_PERIOD_MS);
    }
}

BOARD_LGFX& Display::getGfx()
{
    return displayDriver->getGfx();
}
