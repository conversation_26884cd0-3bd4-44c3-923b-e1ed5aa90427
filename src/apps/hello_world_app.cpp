#include "apps/hello_world_app.h"
#if defined(LILYGO_T_DISPLAY_S3)
    #include "esp32s3.h"
#elif defined(ESP32_4848S040C)
    #include "esp32_4848s040c.h"
#endif

HelloWorldApp::HelloWorldApp()
    : uiManager(nullptr), titleLabel(nullptr), infoLabel(nullptr), counterLabel(nullptr), counter(0)
{
}

HelloWorldApp::~HelloWorldApp()
{
    // Clean up UI manager
    if (uiManager) {
        delete uiManager;
        uiManager = nullptr;
    }
}

void HelloWorldApp::setup()
{
    // Clear the display
    display->clear();

    // Create UI manager
    uiManager = new UIManager(display);

    // Create UI elements
    titleLabel = new Label(10, 10, "Hello World App", TFT_YELLOW, 2);
    infoLabel = new Label(10, 40, "Press buttons to interact:", TFT_WHITE);
    counterLabel = new Label(10, 60, "Counter: 0", TFT_GREEN);

    // Add elements to UI manager
    uiManager->addElement(titleLabel);
    uiManager->addElement(infoLabel);
    uiManager->addElement(counterLabel);

    // Add button instructions
    uiManager->addElement(new Label(10, 80, "BUTTON1: Single click - Increment counter", TFT_CYAN));
    uiManager->addElement(new Label(10, 100, "BUTTON1: Double click - Decrement counter", TFT_CYAN));
    uiManager->addElement(new Label(10, 120, "BUTTON2: Triple click - Switch to Wave Graph App", TFT_CYAN));
    uiManager->addElement(new Label(10, 140, "BUTTON1: Long press - Change title color", TFT_CYAN));
    uiManager->addElement(new Label(10, 160, "BUTTON2: Long press - Reset counter", TFT_CYAN));

    // Draw all elements
    uiManager->drawElements();

    // Print instructions to serial
    Serial.println("Hello World App");
    Serial.println("Button controls:");
    Serial.println("BUTTON1: Single click - Increment counter");
    Serial.println("BUTTON1: Double click - Decrement counter");
    Serial.println("BUTTON2: Triple click - Switch to Wave Graph App");
    Serial.println("BUTTON1: Long press - Change title color");
    Serial.println("BUTTON2: Long press - Reset counter");
}

void HelloWorldApp::update()
{
    // Nothing to update periodically
}

void HelloWorldApp::onActivate()
{
    // Redraw UI elements
    display->clear();
    uiManager->drawElements();

    Serial.println("HelloWorldApp activated");
}

void HelloWorldApp::onDeactivate()
{
    Serial.println("HelloWorldApp deactivated");
}

void HelloWorldApp::handleButtonClick(ButtonId buttonId, int clickCount)
{
    if (buttonId == ButtonId::BUTTON1) {
        if (clickCount == 1) {
            // Increment counter
            counter++;
            counterLabel->setText("Counter: " + std::to_string(counter));
            Serial.printf("Counter incremented to %d\n", counter);
        } else if (clickCount == 2) {
            // Decrement counter
            counter--;
            counterLabel->setText("Counter: " + std::to_string(counter));
            Serial.printf("Counter decremented to %d\n", counter);
        }
    } else if (buttonId == ButtonId::BUTTON2) {
        if (clickCount == 3) {
            // Switch to Wave Graph App
            Serial.println("Switching to Wave Graph App");
            switchToApp("WaveGraphApp");
            return; // Don't redraw UI elements since we're switching apps
        }
    }

    // Redraw UI elements
    display->clear();
    uiManager->drawElements();
}

void HelloWorldApp::handleButtonLongPressStart(ButtonId buttonId)
{
    // No specific action for long press start
}

void HelloWorldApp::handleButtonLongPress(ButtonId buttonId)
{
    if (buttonId == ButtonId::BUTTON1) {
        // Change title color
        static uint16_t colors[] = {TFT_RED, TFT_GREEN, TFT_BLUE, TFT_YELLOW, TFT_MAGENTA, TFT_CYAN, TFT_WHITE};
        static int colorIndex = 0;

        colorIndex = (colorIndex + 1) % 7;
        titleLabel->setColor(colors[colorIndex]);

        Serial.printf("Changed title color to index %d\n", colorIndex);

        // Redraw UI elements
        display->clear();
        uiManager->drawElements();
    } else if (buttonId == ButtonId::BUTTON2) {
        // Reset counter
        counter = 0;
        counterLabel->setText("Counter: " + std::to_string(counter));

        Serial.println("Counter reset to 0");

        // Redraw UI elements
        display->clear();
        uiManager->drawElements();
    }
}

void HelloWorldApp::handleButtonLongPressEnd(ButtonId buttonId)
{
    // No specific action for long press end
}
