#include "apps/wave_graph_app.h"
// Include the appropriate board header based on the board type
#if defined(LILYGO_T_DISPLAY_S3)
    #include "esp32s3.h"
#elif defined(ESP32_4848S040C)
    #include "esp32_4848s040c.h"
#endif
#include "graphs/real_data_provider.h"

WaveGraphApp::WaveGraphApp()
    : graphManager(nullptr), graphCount(0), patternIndex(0), useRealData(false),
      graphYPosition(16), graph1Visible(true), graphUpdatesPaused(false)
{
}

WaveGraphApp::~WaveGraphApp()
{
    // Clean up the graph manager
    if (graphManager) {
        delete graphManager;
        graphManager = nullptr;
    }
}

void WaveGraphApp::setup()
{
    // Create graph manager
    if (!graphManager && display) {
        graphManager = new GraphManager(display->getGfx());
    }

    // Add initial graphs
    addWaveGraph(0, 16, 75, 30, WaveDataProvider::SINE_WAVE);
    addWaveGraph(80, 16, 75, 30, WaveDataProvider::COMBINED_WAVE);
    graphCount = 2;

    // Print instructions
    Serial.println("Wave Graph App");
    Serial.println("Button controls:");
    Serial.println("Button 1: Single click - Add graph, Double click - Remove graph");
    Serial.println("Button 1: Long press - Move graph 0");
    Serial.println("Button 1: Long press end - Clear graph 0 area");
    Serial.println("Button 2: Single click - Change graph 0 pattern, Double click - Toggle graph 1 data source");
    Serial.println("Button 2: Long press - Toggle graph 1 visibility");
    Serial.println("Button 2: Long press end - Clear all graph areas");
}

void WaveGraphApp::update()
{
    // Update and draw graphs if not paused
    if (graphManager && !graphUpdatesPaused) {
        graphManager->updateGraphs();
        graphManager->drawGraphs();
    }
}

void WaveGraphApp::onActivate()
{
    // Resume graph updates when this app is activated
    graphUpdatesPaused = false;
    Serial.println("WaveGraphApp activated");
}

void WaveGraphApp::onDeactivate()
{
    // Pause graph updates when this app is deactivated
    graphUpdatesPaused = true;
    Serial.println("WaveGraphApp deactivated");
}

void WaveGraphApp::handleButtonClick(ButtonId buttonId, int clickCount)
{
    // Button 1: Add/remove graphs or switch app
    if (buttonId == ButtonId::BUTTON1) {
        if (clickCount == 1) {
            // Add a new graph with a different pattern
            int index = addWaveGraph(0, 80, 75, 30,
                                 (WaveDataProvider::WavePattern)(graphCount % 4));
            Serial.printf("Added graph %d\n", index);
            graphCount++;
        } else if (clickCount == 2 && graphCount > 0) {
            // Remove the last graph
            graphCount--;
            removeGraph(graphCount);
            Serial.printf("Removed graph %d\n", graphCount);
        } else if (clickCount == 3) {
            // Switch to Hello World App
            Serial.println("Switching to Hello World App");
            switchToApp("HelloWorldApp");
        }
    }

    // Button 2: Change graph patterns
    if (buttonId == ButtonId::BUTTON2) {
        if (clickCount == 1) {
            // Cycle through wave patterns for graph 0
            patternIndex = (patternIndex + 1) % 4;
            WaveDataProvider::WavePattern pattern =
                (WaveDataProvider::WavePattern)patternIndex;

            switchToWaveData(0, pattern);
            Serial.printf("Changed graph 0 to pattern %d\n", patternIndex);
        } else if (clickCount == 2) {
            // Toggle between wave and real data for graph 1
            useRealData = !useRealData;

            if (useRealData) {
                switchToRealData(1);
                Serial.println("Switched graph 1 to real data");
            } else {
                switchToWaveData(1, WaveDataProvider::COMBINED_WAVE);
                Serial.println("Switched graph 1 to wave data");
            }
        }
    }
}

void WaveGraphApp::handleButtonLongPressStart(ButtonId buttonId)
{
    // No specific action for long press start
}

void WaveGraphApp::handleButtonLongPress(ButtonId buttonId)
{
    // Button 1: Move graphs
    if (buttonId == ButtonId::BUTTON1) {
        graphYPosition += 10;
        if (graphYPosition > 100) graphYPosition = 16;

        // Move graph with automatic clearing of old position
        setGraphPosition(0, 0, graphYPosition, true);
        Serial.printf("Moved graph 0 to y=%d\n", graphYPosition);
    }

    // Button 2: Toggle graph visibility
    if (buttonId == ButtonId::BUTTON2) {
        graph1Visible = !graph1Visible;

        setGraphVisible(1, graph1Visible);
        Serial.printf("Set graph 1 visibility to %d\n", graph1Visible);
    }
}

void WaveGraphApp::handleButtonLongPressEnd(ButtonId buttonId)
{
    // Button 1: Clear graph area on long press end
    if (buttonId == ButtonId::BUTTON1) {
        // Explicitly clear graph 0's area
        clearGraphArea(0);
        Serial.println("Cleared graph 0 area");
    }

    // Button 2: Clear all graphs on long press end
    if (buttonId == ButtonId::BUTTON2) {
        // Clear multiple graph areas
        for (int i = 0; i < 3; i++) {
            clearGraphArea(i);
        }
        Serial.println("Cleared all graph areas");
    }
}

int WaveGraphApp::addWaveGraph(int x, int y, int width, int height,
                     WaveDataProvider::WavePattern pattern,
                     bool showBorder)
{
    if (graphManager == nullptr) {
        return -1;
    }

    // Create a wave data provider
    WaveDataProvider* provider = new WaveDataProvider(width, height, pattern);

    // Configure wave parameters based on pattern
    switch (pattern) {
        case WaveDataProvider::SINE_WAVE:
            provider->setParameters(0.1, height * 0.8);
            break;
        case WaveDataProvider::COSINE_WAVE:
            provider->setParameters(0.12, height * 0.8, PI/4);
            break;
        case WaveDataProvider::COMBINED_WAVE:
            provider->setParameters(0.15, height * 0.8, PI/2);
            break;
        case WaveDataProvider::RANDOM_WAVE:
            provider->setParameters(0.2, height * 0.7);
            break;
    }

    return graphManager->addGraph(x, y, width, height, provider, true, showBorder);
}

int WaveGraphApp::addRealDataGraph(int x, int y, int width, int height, bool showBorder)
{
    if (graphManager == nullptr) {
        return -1;
    }

    // Create a real data provider
    DataProvider* provider = new RealDataProvider(width, height);

    return graphManager->addGraph(x, y, width, height, provider, true, showBorder);
}

bool WaveGraphApp::removeGraph(int index)
{
    if (graphManager == nullptr) {
        return false;
    }

    return graphManager->removeGraph(index);
}

bool WaveGraphApp::setGraphVisible(int index, bool visible)
{
    if (graphManager == nullptr) {
        return false;
    }

    return graphManager->setGraphVisible(index, visible);
}

bool WaveGraphApp::clearGraphArea(int index, uint16_t backgroundColor)
{
    if (graphManager == nullptr) {
        return false;
    }

    return graphManager->clearGraphArea(index, backgroundColor);
}

bool WaveGraphApp::setGraphPosition(int index, int x, int y, bool clearOld)
{
    if (graphManager == nullptr) {
        return false;
    }

    return graphManager->setGraphPosition(index, x, y, clearOld);
}

bool WaveGraphApp::switchToWaveData(int index, WaveDataProvider::WavePattern pattern)
{
    if (graphManager == nullptr) {
        return false;
    }

    Graph* graph = graphManager->getGraph(index);
    if (!graph) {
        return false;
    }

    int width = graph->getWidth();
    int height = graph->getHeight();

    // Create a wave data provider
    WaveDataProvider* provider = new WaveDataProvider(width, height, pattern);

    // Configure wave parameters based on pattern
    switch (pattern) {
        case WaveDataProvider::SINE_WAVE:
            provider->setParameters(0.1, height * 0.8);
            break;
        case WaveDataProvider::COSINE_WAVE:
            provider->setParameters(0.12, height * 0.8, PI/4);
            break;
        case WaveDataProvider::COMBINED_WAVE:
            provider->setParameters(0.15, height * 0.8, PI/2);
            break;
        case WaveDataProvider::RANDOM_WAVE:
            provider->setParameters(0.2, height * 0.7);
            break;
    }

    return graphManager->setGraphDataProvider(index, provider, true);
}

bool WaveGraphApp::switchToRealData(int index)
{
    if (graphManager == nullptr) {
        return false;
    }

    Graph* graph = graphManager->getGraph(index);
    if (!graph) {
        return false;
    }

    int width = graph->getWidth();
    int height = graph->getHeight();

    // Create a real data provider
    DataProvider* provider = new RealDataProvider(width, height);

    return graphManager->setGraphDataProvider(index, provider, true);
}
