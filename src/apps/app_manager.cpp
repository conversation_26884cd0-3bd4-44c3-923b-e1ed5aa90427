#include "apps/app_manager.h"

AppManager::AppManager(Display* display) : display(display), currentApp(nullptr), previousApp(nullptr)
{
}

AppManager::~AppManager()
{
    // Clean up all apps
    for (auto& pair : apps) {
        delete pair.second;
    }
    apps.clear();

    // Reset pointers
    currentApp = nullptr;
    previousApp = nullptr;
}

void AppManager::setCurrentApp(App* app, bool takeOwnership)
{
    // If we're setting the same app, do nothing
    if (app == currentApp) {
        return;
    }

    // Deactivate the current app if it exists
    if (currentApp) {
        currentApp->onDeactivate();
    }

    // Clean up the previous app if it exists and we own it
    if (previousApp) {
        delete previousApp;
        previousApp = nullptr;
    }

    // Store the current app as the previous app
    previousApp = currentApp;

    // Set the new current app
    currentApp = app;

    // Register button handlers with the new app and activate it
    if (currentApp) {
        // Set the display and app switch callback
        currentApp->setDisplay(display);
        currentApp->setOnAppSwitchCallback([this](const std::string& appName) {
            return this->switchToApp(appName);
        });

        // Register button handlers
        currentApp->registerButtonHandlers(buttonHandlers);

        // Initialize and activate
        currentApp->setup();
        currentApp->onActivate();
    }
}

void AppManager::registerButtonHandler(ButtonId buttonId, AppButtonHandler* handler)
{
    if (handler) {
        // Store in the map
        buttonHandlers[buttonId] = handler;

        // Register with current app if it exists
        if (currentApp) {
            std::map<ButtonId, AppButtonHandler*> buttonMap;
            buttonMap[buttonId] = handler;
            currentApp->registerButtonHandlers(buttonMap);
        }
    }
}

void AppManager::registerButtonHandler(AppButtonHandler* handler)
{
    if (handler) {
        int pinNumber = handler->getPinNumber();
        ButtonId buttonId;

        // Map pin number to button ID
        if (pinNumber == PIN_BUTTON_1) {
            buttonId = ButtonId::BUTTON1;
        } else if (pinNumber == PIN_BUTTON_2) {
            buttonId = ButtonId::BUTTON2;
        } else {
            // Default to BUTTON1 if pin number is unknown
            buttonId = ButtonId::BUTTON1;
        }

        registerButtonHandler(buttonId, handler);
    }
}

void AppManager::update()
{
    // Update the current app if it exists
    if (currentApp) {
        currentApp->update();
    }
}

void AppManager::registerApp(const std::string& name, App* app)
{
    if (app) {
        // Store the app in the map
        apps[name] = app;

        // Set the app switch callback
        app->setOnAppSwitchCallback([this](const std::string& appName) {
            return this->switchToApp(appName);
        });
    }
}

bool AppManager::switchToApp(const std::string& name)
{
    // Get the app from the map
    App* app = getApp(name);

    // Set the app as the current app if it exists
    if (app) {
        setCurrentApp(app, false); // Don't take ownership since we're managing the apps
        return true;
    }

    Serial.printf("Error: App '%s' not found\n", name.c_str());
    return false;
}

App* AppManager::getApp(const std::string& name)
{
    // Find the app in the map
    auto it = apps.find(name);
    if (it != apps.end()) {
        return it->second;
    }

    return nullptr;
}


