#include <WiFi.h>

#include "utils/wifihandler.h"

WifiHandler::WifiHandler()
    : threadHandle(nullptr)
{
    // Constructor is now minimal, all initialization happens in setup()
}

void WifiHandler::setup(const char *ssid, const char *password)
{
    // Parameters are passed directly to setup() now
    Serial.printf("Connecting to WiFi - %s\n", ssid);
    WiFi.mode(WIFI_STA);
    WiFi.setAutoConnect(true);
    WiFi.setAutoReconnect(true);
    WiFi.begin(ssid, password);

    if (xTaskCreate(thread, "WifiThread", 4096, &WiFi, 1, &threadHandle) != pdPASS)
    {
        Serial.println("Failed to create wifi thread");
        vTaskDelete(threadHandle);
    }
}

WifiHandler::~WifiHandler()
{
    WiFi.setAutoConnect(false);
    WiFi.setAutoReconnect(false);
    WiFi.disconnect();
    WiFi.mode(WIFI_OFF);
}

void WifiHandler::thread(void *context)
{
    WiFiClass *wifi = static_cast<WiFiClass *>(context);
    wl_status_t status = wifi->status();

    while (true)
    {
        if (wifi->status() != status)
        {
            switch (wifi->status())
            {
            case WL_CONNECTED:
                Serial.printf("WiFi: Connected to %s (Channel %d, RSSI %d).", wifi->SSID().c_str(), wifi->channel(), wifi->RSSI());
                break;
            case WL_DISCONNECTED:
                Serial.println("WiFi: Disconnected.");
                break;
            case WL_CONNECT_FAILED:
                Serial.println("Wifi: Connection failed.");
                break;
            case WL_CONNECTION_LOST:
                Serial.println("Wifi: Connection lost.");
                break;
            case WL_NO_SSID_AVAIL:
                Serial.printf("Wifi: SSID %d not available, aborting.\n", wifi->SSID().c_str());
                vTaskDelete(NULL);
                break;
            case WL_SCAN_COMPLETED:
                Serial.println("Wifi: Scan completed.");
                break;
            case WL_IDLE_STATUS:
                Serial.println("Wifi: Idle status.");
                break;
            default:
                Serial.println("Wifi: Unknown status.");
                break;
            }
            status = wifi->status();
        }
        vTaskDelay(5000 / portTICK_PERIOD_MS);
    }
}
