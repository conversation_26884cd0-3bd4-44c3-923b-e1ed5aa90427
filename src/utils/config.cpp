#include <Arduino.h>
#include <LittleFS.h>

#include "utils/config.h"

Config::Config(const char *filename)
{
    this->filename = filename;
    load();
}

void Config::load(void)
{
    if (!LittleFS.begin(true))
    {
        Serial.println("Failed to mount file system");
        return;
    }

    File file = LittleFS.open(filename, "r");
    if (file)
    {
        DeserializationError error = deserializeJson(config, file);
        if (error)
        {
            Serial.printf("Failed to read config file [%s]\n", filename);
        }

        file.close();
    }
    else
    {
        config["wifi.ssid"] = "Betelgeuse";
        config["whatsapp.number"] = "27760669842";
        config["whatsapp.apikey"] = "3354258";
        this->save();
    }
}

void Config::save(void)
{
    File file = LittleFS.open(filename, "w");
    size_t size = serializeJson(config, file);
    if (file)
    {
        file.close();
    }
}