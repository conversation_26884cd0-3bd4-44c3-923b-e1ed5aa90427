#include <Arduino.h>
#include <Callmebot_ESP32.h>

// Include the appropriate board header based on the board type
#if defined(LILYGO_T_DISPLAY_S3)
    #include "esp32s3.h"
#elif defined(ESP32_4848S040)
    #include "esp32_4848s040.h"
#endif

#include "utils/config.h"
#include "display/display.h"
#include "utils/wifihandler.h"
#include "apps/app_manager.h"
#include "buttons/app_button_handler.h"
#include "apps/wave_graph_app.h"
#include "apps/hello_world_app.h"

// App instances
WaveGraphApp* waveGraphApp;
HelloWorldApp* helloWorldApp;

Config *config;
Display *display;
WifiHandler *wifi;
AppManager *appManager;
AppButtonHandler *button1Handler;
AppButtonHandler *button2Handler;

void setup()
{
  Serial.begin(115200);
  Serial.println("Starting setup...");

  // Initialize display
  display = new Display();
  display->setup();
  // TODO - Add a configuration option for this
  // display->configureLdr(PIN_LDR);
  Serial.println("Display initialized");

  // Create app manager
  appManager = new AppManager(display);
  Serial.println("App manager created");

  // Register button handlers with the app manager
  appManager->registerButtonHandler(ButtonId::BUTTON1, new AppButtonHandler(PIN_BUTTON_1));
  appManager->registerButtonHandler(ButtonId::BUTTON2, new AppButtonHandler(PIN_BUTTON_2));
  Serial.println("Button handlers registered");

  // Create apps
  waveGraphApp = new WaveGraphApp();
  helloWorldApp = new HelloWorldApp();
  Serial.println("Apps created");

  // Register apps with the app manager
  appManager->registerApp("WaveGraphApp", waveGraphApp);
  appManager->registerApp("HelloWorldApp", helloWorldApp);
  Serial.println("Apps registered");

  // Set the initial app
  appManager->switchToApp("HelloWorldApp");

  // Initialize WiFi
  config = new Config();
  const char *ssid = config->getWifiSsid();
  if (ssid != NULL) {
    wifi = new WifiHandler();
    wifi->setup(ssid, "W!t618,.");
    Serial.println("WiFi initialized");
  }
}

void loop()
{
  // Update the current app
  if (appManager) {
    appManager->update();
    vTaskDelay(1000 / portTICK_PERIOD_MS);
  }
}
